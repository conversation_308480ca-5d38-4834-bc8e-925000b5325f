<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Facebook\OptimizedFacebookAdService;
use App\Models\ApiKey;

class TestFacebookConnection extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'facebook:test {--account= : Test specific account ID}';

    /**
     * The console command description.
     */
    protected $description = 'Test Facebook API connection and debug configuration';

    /**
     * Execute the console command.
     */
    public function handle(OptimizedFacebookAdService $facebookService)
    {
        $this->info('🔍 Testing Facebook API Connection...');
        $this->newLine();

        // 1. Check Database Configuration
        $this->info('1. Checking Database Configuration:');
        $apiKey = ApiKey::first();

        if (!$apiKey) {
            $this->error('❌ No API key configuration found in database');
            return Command::FAILURE;
        }

        $configStatus = [
            'App ID' => !empty($apiKey->facebook_app_id) ? '✅ SET' : '❌ MISSING',
            'App Secret' => !empty($apiKey->facebook_app_secret) ? '✅ SET' : '❌ MISSING',
            'Access Token' => !empty($apiKey->facebook_access_token) ? '✅ SET' : '❌ MISSING',
        ];

        foreach ($configStatus as $key => $status) {
            $this->line("   {$key}: {$status}");
        }
        $this->newLine();

        // 2. Check Service Configuration
        $this->info('2. Checking Service Configuration:');
        $isConfigured = $facebookService->isConfigured();
        $this->line("   Service Configured: " . ($isConfigured ? '✅ YES' : '❌ NO'));
        $this->newLine();

        if (!$isConfigured) {
            $this->error('❌ Facebook service is not properly configured');
            return Command::FAILURE;
        }

        // 3. Test Basic Connection
        $this->info('3. Testing Basic Connection:');
        try {
            $connectionTest = $facebookService->testConnection();

            if ($connectionTest['success']) {
                $this->info('   ✅ Basic connection successful');
            } else {
                $this->error('   ❌ Basic connection failed: ' . $connectionTest['message']);
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error('   ❌ Connection test failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
        $this->newLine();

        // 4. Test Account Data Fetch (if account provided)
        $accountId = $this->option('account');
        if ($accountId) {
            $this->info("4. Testing Account Data Fetch for: {$accountId}");

            // Add 'act_' prefix if not present
            if (!str_starts_with($accountId, 'act_')) {
                $accountId = 'act_' . $accountId;
            }

            try {
                $this->line('   Fetching campaign data...');
                $data = $facebookService->getOptimizedCampaignData($accountId);

                if (!empty($data)) {
                    $this->info("   ✅ Successfully fetched " . count($data) . " campaigns");

                    // Show sample data
                    if (count($data) > 0) {
                        $this->line('   Sample campaigns:');
                        foreach (array_slice($data, 0, 3) as $campaign) {
                            $this->line("     - {$campaign['nama_kampanye']} (Status: {$campaign['status']})");
                        }
                    }
                } else {
                    $this->warn('   ⚠️ No campaigns found for this account');
                }
            } catch (\Exception $e) {
                $this->error('   ❌ Failed to fetch account data: ' . $e->getMessage());

                // Check if it's permission/access issue
                if (str_contains($e->getMessage(), 'permission') || str_contains($e->getMessage(), 'access')) {
                    $this->warn('   💡 This might be a permission issue. Please check:');
                    $this->warn('      - Access token has ads_read permission');
                    $this->warn('      - User has access to the specified account');
                    $this->warn('      - Account ID is correct');
                }

                return Command::FAILURE;
            }
            $this->newLine();
        }

        // 5. Configuration Tips
        $this->info('5. Configuration Tips:');
        $this->line('   📝 App ID: Found in Facebook App Dashboard');
        $this->line('   🔑 App Secret: Found in Facebook App Dashboard > Settings > Basic');
        $this->line('   🎫 Access Token: Generate with ads_read permission');
        $this->line('   🏢 Account Access: Make sure token has access to ad accounts');
        $this->newLine();

        $this->info('🎉 Facebook API connection test completed successfully!');

        if (!$accountId) {
            $this->line('💡 Run with --account=YOUR_ACCOUNT_ID to test specific account data fetch');
        }

        return Command::SUCCESS;
    }
}
