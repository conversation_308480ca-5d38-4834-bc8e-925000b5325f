<?php

namespace App\Http\Requests;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DataMonitoringCprRequest extends FormRequest
{
    public $bulan;
    public $tahun;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // $id = $this->route('laporan.cashback');
        return [
            'nama_set_iklan'         => ['required'],
            'id_akun'                => ['required'],
            'id_kampanye'            => ['required'],
            'anggaran_harian'        => ['required'],
            'spek_atribusi'          => ['required'],
            'status'                 => ['required'],
            'tanggal_mulai'          => ['required'],
            'tanggal_berhenti'       => ['required'],
            'nama_kampanye'          => ['required'],
            'adlabels'               => ['required'],
            'strategi_penawaran'     => ['required'],
            'jangkauan'              => ['required'],
            'impresi'                => ['required'],
            'biaya_dibelanjakan'     => ['required'],
            'biaya_per_klik'         => ['required'],
            'biaya_perhasil'         => ['required'],
            'hasil'                  => ['required'],
        ];        
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {           
        return [            
            'nama_set_iklan.required'        => 'Nama Set Iklan harus diisi',   
            'id_akun.required'               => 'ID Akun harus diisi',   
            'id_kampanye.required'           => 'ID Kampanye harus diisi',
            'anggaran_harian.required'       => 'Anggaran Harian harus diisi',
            'spek_atribusi.required'         => 'Spesifikasi Atribusi harus diisi',
            'status.required'                => 'Status harus diisi',
            'tanggal_mulai.required'        => 'Tanggal Mulai harus diisi',
            'tanggal_berhenti.required'     => 'Tanggal Berhenti harus diisi',
            'nama_kampanye.required'        => 'Nama Kampanye harus diisi',
            'adlabels.required'             => 'Label Iklan harus diisi',
            'strategi_penawaran.required'   => 'Strategi Penawaran harus diisi',
            'jangkauan.required'            => 'Jangkauan harus diisi',
            'impresi.required'              => 'Impresi harus diisi',
            'biaya_dibelanjakan.required'   => 'Jumlah Dibellanjakan harus diisi',
            'biaya_per_klik.required'       => 'Biaya Per Klik harus diisi',
            'biaya_perhasil.required'       => 'Biaya Per Hasil harus diisi',            
            'hasil.required'                => 'Hasil harus diisi',            
        ];
    }
}
