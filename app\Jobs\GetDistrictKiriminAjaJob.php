<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use App\Services\KiriminAja\Area\KiriminAjaAreaService;
use App\Models\Province;
use App\Models\City;
use App\Models\District;

class GetDistrictKiriminAjaJob implements ShouldQueue
{
    use Queueable;

    protected $cityId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $cityId)
    {
        $this->cityId = $cityId;
        /**
         * Save the district data to the database.
         */
    }
    protected function saveDistrict(array $district): void
    {
        District::updateOrCreate(
            ['id' => $district['id']],
            [
                'kecamatan_name' => $district['kecamatan_name'],
                'kabupaten_id' => $district['kabupaten_id'],
            ]
        );
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $service = new KiriminAjaAreaService();
        $districts = $service->getDistricts($this->cityId);
        if (isset($districts['status']) && $districts['status'] && isset($districts['datas'])) {
            foreach ($districts['datas'] as $district) {
                District::updateOrCreate(
                    ['id' => $district['id']],
                    [
                        'kecamatan_name' => $district['kecamatan_name'],
                        'kabupaten_id' => $district['kabupaten_id'],
                    ]
                );
            }
            Log::info('District data saved successfully.');
        } else {
            Log::error('Failed to fetch district data.', ['response' => $districts]);
        }
    }
}
