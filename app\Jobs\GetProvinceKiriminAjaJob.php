<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use App\Services\KiriminAja\Area\KiriminAjaAreaService;
use App\Models\Province;

class GetProvinceKiriminAjaJob implements ShouldQueue
{
    use Queueable;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // $provinces = $this->kiriminAjaAreaService->getProvinces();
        $kiriminAjaAreaService = app(KiriminAjaAreaService::class);
        $provinces = $kiriminAjaAreaService->getProvinces();
        if ($provinces['status'] && isset($provinces['datas'])) {
            foreach ($provinces['datas'] as $province) {
                Province::updateOrCreate(
                    ['id' => $province['id']],
                    ['province_name' => $province['provinsi_name']]
                );
            }
            Log::info('Provinces data saved successfully.');
        } else {
            Log::error('Failed to fetch provinces data.', ['response' => $provinces]);
        }
    }
}
