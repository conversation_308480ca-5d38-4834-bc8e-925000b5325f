<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\MonitoringCpr\OptimizedDataMonitoringCprService;
use Illuminate\Support\Facades\Log;

class SyncFacebookCprData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 3; // Retry 3 times if failed

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $accountId,
        public ?array $dateRange = null,
        public ?int $userId = null
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(OptimizedDataMonitoringCprService $cprService): void
    {
        Log::info('Starting background Facebook CPR data sync', [
            'account_id' => $this->accountId,
            'date_range' => $this->dateRange,
            'user_id' => $this->userId,
            'job_id' => $this->job->getJobId()
        ]);

        try {
            $result = $cprService->syncFromFacebookApi($this->accountId, $this->dateRange);

            if ($result['success']) {
                Log::info('Background Facebook CPR data sync completed successfully', [
                    'account_id' => $this->accountId,
                    'campaigns_processed' => $result['stats']['total_campaigns'] ?? 0,
                    'records_updated' => $result['stats']['updated_records'] ?? 0,
                    'job_id' => $this->job->getJobId()
                ]);

                // You can dispatch events here to notify frontend about completion
                // broadcast(new FacebookDataSyncCompleted($this->accountId, $result));

            } else {
                Log::warning('Background Facebook CPR data sync completed with warnings', [
                    'account_id' => $this->accountId,
                    'message' => $result['message'],
                    'job_id' => $this->job->getJobId()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Background Facebook CPR data sync failed', [
                'account_id' => $this->accountId,
                'date_range' => $this->dateRange,
                'error' => $e->getMessage(),
                'job_id' => $this->job->getJobId()
            ]);

            // Re-throw the exception to trigger job retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Facebook CPR data sync job failed permanently', [
            'account_id' => $this->accountId,
            'date_range' => $this->dateRange,
            'error' => $exception->getMessage(),
            'job_id' => $this->job->getJobId()
        ]);

        // You can notify users about the failure here
        // Mail::to($user)->send(new FacebookSyncFailedMail($exception));
    }
}
