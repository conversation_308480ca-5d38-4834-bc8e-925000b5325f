<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class OptimizedMonitoringCpr extends Model
{
    protected $table = 'optimized_monitoring_cpr';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'campaign_id',
        'campaign_name',
        'account_id',
        'status',
        'campaign_start_date',
        'spend',
        'results',
        'cpr',
        'data_date',
        'last_synced_at',
    ];

    protected $casts = [
        'campaign_start_date' => 'date',
        'data_date' => 'date',
        'last_synced_at' => 'datetime',
        'spend' => 'decimal:4',
        'cpr' => 'decimal:4',
        'results' => 'integer',
    ];

    protected $dates = [
        'campaign_start_date',
        'data_date',
        'last_synced_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Scope untuk filter berdasarkan account ID
     */
    public function scopeForAccount(Builder $query, string $accountId): Builder
    {
        return $query->where('account_id', $accountId);
    }

    /**
     * Scope untuk filter berdasarkan date range
     */
    public function scopeInDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('data_date', [$startDate, $endDate]);
    }

    /**
     * Scope untuk campaign yang masih aktif
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->whereIn('status', ['ACTIVE', 'PAUSED']);
    }

    /**
     * Scope untuk data terbaru (berdasarkan last_synced_at)
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('last_synced_at', 'desc');
    }

    /**
     * Scope untuk campaign dengan performa terbaik (berdasarkan spend)
     */
    public function scopeTopPerformers(Builder $query, int $limit = 10): Builder
    {
        return $query->orderBy('spend', 'desc')->limit($limit);
    }

    /**
     * Accessor untuk formatted spend dalam Rupiah
     */
    public function getFormattedSpentAttribute(): string
    {
        if (!$this->spend || $this->spend == 0) {
            return '-';
        }

        $exchangeRate = config('currency.peso_to_rupiah', 278.62);
        $rupiah = $this->spend * $exchangeRate;

        return 'Rp ' . number_format($rupiah, 0, ',', '.');
    }

    /**
     * Accessor untuk formatted CPR dalam Rupiah
     */
    public function getFormattedCprAttribute(): string
    {
        if (!$this->cpr || $this->cpr == 0) {
            return '-';
        }

        $exchangeRate = config('currency.peso_to_rupiah', 278.62);
        $rupiah = $this->cpr * $exchangeRate;

        return 'Rp ' . number_format($rupiah, 0, ',', '.');
    }

    /**
     * Accessor untuk status display dengan warna
     */
    public function getStatusDisplayAttribute(): array
    {
        $statusConfig = [
            'ACTIVE' => ['label' => 'Active', 'class' => 'status-active'],
            'PAUSED' => ['label' => 'Paused', 'class' => 'status-paused'],
            'DELETED' => ['label' => 'Deleted', 'class' => 'status-inactive'],
            'ARCHIVED' => ['label' => 'Archived', 'class' => 'status-inactive'],
        ];

        return $statusConfig[$this->status] ?? ['label' => 'Unknown', 'class' => 'status-inactive'];
    }

    /**
     * Check apakah data masih fresh (dalam 1 jam terakhir)
     */
    public function getIsFreshAttribute(): bool
    {
        return $this->last_synced_at && $this->last_synced_at->diffInHours(now()) < 1;
    }

    /**
     * Get performance rating berdasarkan CPR
     */
    public function getPerformanceRatingAttribute(): string
    {
        if (!$this->cpr || $this->cpr == 0) {
            return 'no-data';
        }

        // Rating berdasarkan CPR (dalam USD, sesuaikan dengan bisnis logic)
        if ($this->cpr <= 5) {
            return 'excellent';
        } elseif ($this->cpr <= 10) {
            return 'good';
        } elseif ($this->cpr <= 20) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Static method untuk bulk update atau create data
     */
    public static function bulkUpdateOrCreate(array $campaignsData, string $accountId, string $dataDate): int
    {
        $updatedCount = 0;

        foreach ($campaignsData as $campaignData) {
            $record = self::updateOrCreate(
                [
                    'campaign_id' => $campaignData['id_kampanye'],
                    'data_date' => $dataDate,
                ],
                [
                    'campaign_name' => $campaignData['nama_kampanye'],
                    'account_id' => $accountId,
                    'status' => strtoupper($campaignData['status'] ?? 'ACTIVE'),
                    'campaign_start_date' => $campaignData['tanggal_mulai'] ?? null,
                    'spend' => $campaignData['biaya_dibelanjakan'] ?? 0,
                    'results' => $campaignData['hasil'] ?? 0,
                    'cpr' => $campaignData['cpr'] ?? 0,
                    'last_synced_at' => now(),
                ]
            );

            if ($record->wasRecentlyCreated || $record->wasChanged()) {
                $updatedCount++;
            }
        }

        return $updatedCount;
    }

    /**
     * Static method untuk mendapatkan data untuk DataTables
     */
    public static function getDataTableData(string $accountId, string $dateRange): Builder
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        return self::forAccount($accountId)
            ->inDateRange($startDate, $endDate)
            ->active()
            ->latest()
            ->select([
                'id',
                'campaign_id',
                'campaign_name',
                'status',
                'spend',
                'results',
                'cpr',
                'last_synced_at'
            ]);
    }

    /**
     * Static method untuk clean up data lama
     */
    public static function cleanupOldData(int $daysToKeep = 90): int
    {
        $cutoffDate = Carbon::now()->subDays($daysToKeep);

        return self::where('data_date', '<', $cutoffDate)->delete();
    }

    public function getDataForDataTable(string $accountId): \Illuminate\Database\Eloquent\Builder
    {
        return $this->where('account_id', $accountId)
            ->where('status', '!=', 'DELETED')
            ->select([
                'id',
                'campaign_id',
                'campaign_name',
                'status',
                'spend',
                'results',
                'cpr',
                'last_synced_at'
            ])
            ->orderBy('last_synced_at', 'desc');
    }
}
