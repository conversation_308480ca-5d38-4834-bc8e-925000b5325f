<?php

namespace App\Services\MonitoringCpr;

use App\Models\OptimizedMonitoringCpr;
use App\Services\Facebook\OptimizedFacebookAdService;
use App\Services\CurrencyService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class OptimizedDataMonitoringCprService
{
    public function __construct(
        protected OptimizedFacebookAdService $facebookService,
        protected CurrencyService $currencyService
    ) {
    }

    /**
     * Get data for DataTables with optimized queries
     */
    public function getDataForDataTable(string $accountId): \Illuminate\Database\Eloquent\Builder
    {
        try {
            return OptimizedMonitoringCpr::forAccount($accountId)
                ->where('status', '!=', 'DELETED')
                ->select([
                    'id',
                    'campaign_id',
                    'campaign_name',
                    'status',
                    'spend',
                    'results',
                    'cpr',
                    'last_synced_at'
                ])
                ->orderBy('last_synced_at', 'desc');

        } catch (\Exception $e) {
            Log::error('Error getting data for DataTable', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Sync data from Facebook API
     */
    public function syncFromFacebookApi(string $accountId, array $dateRange = null): array
    {
        try {
            // Get Facebook configuration from database
            $config = Cache::remember('facebook_config_optimized', 3600, function () {
                return \App\Models\ApiKey::first();
            });

            if (!$config || empty($config->facebook_app_id) || empty($config->facebook_app_secret) || empty($config->facebook_access_token)) {
                return [
                    'success' => false,
                    'message' => 'Facebook credentials are not properly configured in database'
                ];
            }

            // Initialize Facebook SDK
            $api = \FacebookAds\Api::init(
                $config->facebook_app_id,
                $config->facebook_app_secret,
                $config->facebook_access_token
            );

            // Get campaigns data from Facebook
            // Check if account ID already has 'act_' prefix
            $formattedAccountId = strpos($accountId, 'act_') === 0 ? $accountId : 'act_' . $accountId;
            $account = new \FacebookAds\Object\AdAccount($formattedAccountId);

            $fields = ['id', 'name', 'status'];
            $params = [
                'effective_status' => ['ACTIVE', 'PAUSED', 'ARCHIVED'],
                'limit' => 1000
            ];

            $campaigns = $account->getCampaigns($fields, $params);

            if (!$campaigns || count($campaigns) === 0) {
                return [
                    'success' => false,
                    'message' => 'No campaigns found for account ID: ' . $accountId
                ];
            }

            // Process and store data
            $processedCampaigns = 0;
            $updatedRecords = 0;

            foreach ($campaigns as $campaign) {
                try {
                    // Get insights for this campaign
                    $insightFields = ['spend', 'actions'];
                    $insightParams = [
                        'time_range' => [
                            'since' => $dateRange[0] ?? date('Y-m-d', strtotime('-30 days')),
                            'until' => $dateRange[1] ?? date('Y-m-d')
                        ],
                        'level' => 'campaign'
                    ];

                    $insights = $campaign->getInsights($insightFields, $insightParams);
                    $insightData = $insights->getResponse()->getContent();

                    if (isset($insightData['data']) && !empty($insightData['data'])) {
                        $latestInsight = $insightData['data'][0];
                        $spend = isset($latestInsight['spend']) ? floatval($latestInsight['spend']) : 0;
                        $results = isset($latestInsight['actions']) ? $this->extractPurchaseData($latestInsight['actions']) : 0;
                    } else {
                        $spend = 0;
                        $results = 0;
                    }

                    $cpr = $results > 0 ? $spend / $results : 0;

                    $record = OptimizedMonitoringCpr::updateOrCreate(
                        [
                            'campaign_id' => $campaign->id,
                            'data_date' => $dateRange[0] ?? now()->toDateString()
                        ],
                        [
                            'account_id' => $formattedAccountId,
                            'campaign_name' => $campaign->name,
                            'status' => $campaign->status,
                            'spend' => $spend,
                            'results' => $results,
                            'cpr' => $cpr,
                            'last_synced_at' => now()
                        ]
                    );

                    $processedCampaigns++;
                    if ($record->wasRecentlyCreated || $record->wasChanged()) {
                        $updatedRecords++;
                    }

                } catch (\Exception $campaignError) {
                    Log::warning('Error processing campaign', [
                        'campaign_id' => $campaign->id ?? 'unknown',
                        'error' => $campaignError->getMessage()
                    ]);
                    continue; // Continue with next campaign
                }
            }

            return [
                'success' => true,
                'message' => "Successfully synced {$processedCampaigns} campaigns",
                'stats' => [
                    'total_campaigns' => $processedCampaigns,
                    'updated_records' => $updatedRecords
                ]
            ];

        } catch (\Exception $e) {
            // Check for specific Facebook API errors in the message
            $errorMessage = $e->getMessage();

            if (
                strpos($errorMessage, 'Invalid OAuth access token') !== false ||
                strpos($errorMessage, 'authorization') !== false
            ) {
                Log::error('Facebook authorization error during sync', [
                    'account_id' => $accountId,
                    'error' => $errorMessage
                ]);

                return [
                    'success' => false,
                    'message' => 'Facebook authorization failed. Please check your access token permissions.'
                ];
            }

            if (
                strpos($errorMessage, 'Invalid app_id') !== false ||
                strpos($errorMessage, 'authentication') !== false
            ) {
                Log::error('Facebook authentication error during sync', [
                    'account_id' => $accountId,
                    'error' => $errorMessage
                ]);

                return [
                    'success' => false,
                    'message' => 'Facebook authentication failed. Please check your App ID and App Secret.'
                ];
            }

            // Generic error handling
            Log::error('Error syncing data from Facebook', [
                'account_id' => $accountId,
                'error' => $errorMessage,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to fetch campaign data: ' . $errorMessage
            ];
        }
    }

    /**
     * Extract purchase data from actions array
     */
    private function extractPurchaseData($actions): int
    {
        if (!is_array($actions)) {
            return 0;
        }

        $purchases = 0;

        foreach ($actions as $action) {
            if (!is_array($action)) {
                continue;
            }

            // Look for purchase actions
            if (
                isset($action['action_type']) &&
                in_array($action['action_type'], [
                    'web_in_store_purchase',
                    'purchase',
                    'onsite_conversion.purchase'
                ]) &&
                isset($action['value'])
            ) {
                $purchases += intval($action['value']);
            }
        }

        return $purchases;
    }

    /**
     * Format data for modal display
     */
    private function formatDataForModal(array $campaignData): array
    {
        return array_map(function ($campaign) {
            return [
                'id_kampanye' => $campaign['id_kampanye'],
                'nama_kampanye' => $campaign['nama_kampanye'],
                'status' => $campaign['status'],
                'hasil' => $campaign['hasil'],
                'cpr' => $campaign['cpr'],
                'jumlah_dibelanjakan' => $campaign['biaya_dibelanjakan'],
                // Add formatted versions
                'formatted_cpr' => $this->currencyService->convertAndFormatPesoToRupiah($campaign['cpr']),
                'formatted_spent' => $this->currencyService->convertAndFormatPesoToRupiah($campaign['biaya_dibelanjakan'])
            ];
        }, $campaignData);
    }

    /**
     * Clear related caches after data update
     */
    private function clearRelatedCaches(string $accountId, array $dateRange = null): void
    {
        try {
            // Clear Facebook service cache
            $this->facebookService->clearCacheForAccount($accountId);

            // Clear other related caches using Cache::forget (works with all drivers)
            $cacheKeys = [
                "fb_account_info_{$accountId}",
                "cpr_summary_{$accountId}",
                "facebook_config_optimized"
            ];

            // Add date-specific cache keys if date range provided
            if ($dateRange && count($dateRange) === 2) {
                $dateKey = implode('_', $dateRange);
                $cacheKeys[] = "cpr_summary_{$accountId}_{$dateKey}";
            }

            // Clear each cache key
            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            Log::debug('Cleared related caches', [
                'account_id' => $accountId,
                'cache_keys_cleared' => count($cacheKeys)
            ]);

        } catch (\Exception $e) {
            Log::warning('Failed to clear some caches', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get performance summary for account
     */
    public function getPerformanceSummary(string $accountId, string $dateRange): array
    {
        $cacheKey = "cpr_summary_{$accountId}_" . str_replace(' to ', '_', $dateRange);

        return Cache::remember($cacheKey, 1800, function () use ($accountId, $dateRange) {
            [$startDate, $endDate] = explode(' to ', $dateRange);

            $data = OptimizedMonitoringCpr::forAccount($accountId)
                ->inDateRange($startDate, $endDate)
                ->active();

            $totalSpend = $data->sum('spend');
            $totalResults = $data->sum('results');
            $campaignCount = $data->count();
            $avgCpr = $totalResults > 0 ? $totalSpend / $totalResults : 0;

            return [
                'total_campaigns' => $campaignCount,
                'total_spend' => $totalSpend,
                'total_results' => $totalResults,
                'average_cpr' => $avgCpr,
                'formatted_spend' => $this->currencyService->convertAndFormatPesoToRupiah($totalSpend),
                'formatted_avg_cpr' => $this->currencyService->convertAndFormatPesoToRupiah($avgCpr),
            ];
        });
    }

    /**
     * Get top performing campaigns
     */
    public function getTopPerformingCampaigns(string $accountId, string $dateRange, int $limit = 5): array
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        return OptimizedMonitoringCpr::forAccount($accountId)
            ->inDateRange($startDate, $endDate)
            ->active()
            ->topPerformers($limit)
            ->get()
            ->map(function ($campaign) {
                return [
                    'nama_kampanye' => $campaign->campaign_name,
                    'status' => $campaign->status,
                    'hasil' => $campaign->results,
                    'formatted_cpr' => $campaign->formatted_cpr,
                    'formatted_spent' => $campaign->formatted_spent,
                    'performance_rating' => $campaign->performance_rating,
                ];
            })
            ->toArray();
    }

    /**
     * Clean up old data
     */
    public function cleanupOldData(int $daysToKeep = 90): int
    {
        try {
            $deletedCount = OptimizedMonitoringCpr::cleanupOldData($daysToKeep);

            Log::info('Cleaned up old CPR data', [
                'days_kept' => $daysToKeep,
                'deleted_records' => $deletedCount
            ]);

            return $deletedCount;

        } catch (\Exception $e) {
            Log::error('Failed to cleanup old data', [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Check if data is fresh for account
     */
    public function isDataFresh(string $accountId, string $dateRange): bool
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $latestRecord = OptimizedMonitoringCpr::forAccount($accountId)
            ->inDateRange($startDate, $endDate)
            ->latest('last_synced_at')
            ->first();

        return $latestRecord && $latestRecord->is_fresh;
    }

    /**
     * Force refresh data from Facebook API
     */
    public function forceRefresh(string $accountId, array $dateRange = null): array
    {
        // Clear cache first
        $this->facebookService->clearCacheForAccount($accountId);

        // Then sync fresh data
        return $this->syncFromFacebookApi($accountId, $dateRange);
    }

    /**
     * Get trend data for charts
     */
    public function getTrendData(string $accountId, string $dateRange, string $groupBy = 'day'): array
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $cacheKey = "cpr_trend_data_{$accountId}_{$startDate}_{$endDate}_{$groupBy}";

        return Cache::remember($cacheKey, 900, function () use ($accountId, $startDate, $endDate, $groupBy) {
            $query = OptimizedMonitoringCpr::forAccount($accountId)
                ->inDateRange($startDate, $endDate)
                ->active();

            $dateFormat = match ($groupBy) {
                'hour' => '%Y-%m-%d %H:00:00',
                'day' => '%Y-%m-%d',
                'week' => '%Y-%u',
                'month' => '%Y-%m',
                default => '%Y-%m-%d'
            };

            $data = $query->selectRaw("
                DATE_FORMAT(data_date, '{$dateFormat}') as period,
                SUM(spend) as total_spend,
                SUM(results) as total_results,
                AVG(cpr) as avg_cpr,
                COUNT(*) as campaign_count
            ")
                ->groupBy('period')
                ->orderBy('period')
                ->get();

            return [
                'dates' => $data->pluck('period')->toArray(),
                'spend' => $data->pluck('total_spend')->toArray(),
                'results' => $data->pluck('total_results')->toArray(),
                'cpr' => $data->pluck('avg_cpr')->toArray(),
                'campaign_count' => $data->pluck('campaign_count')->toArray()
            ];
        });
    }

    /**
     * Get status distribution for pie chart
     */
    public function getStatusDistribution(string $accountId, string $dateRange): array
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $cacheKey = "cpr_status_distribution_{$accountId}_{$startDate}_{$endDate}";

        return Cache::remember($cacheKey, 900, function () use ($accountId, $startDate, $endDate) {
            return OptimizedMonitoringCpr::forAccount($accountId)
                ->inDateRange($startDate, $endDate)
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();
        });
    }

    /**
     * Get performance insights and recommendations
     */
    public function getPerformanceInsights(string $accountId, string $dateRange): array
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $campaigns = OptimizedMonitoringCpr::forAccount($accountId)
            ->inDateRange($startDate, $endDate)
            ->active()
            ->get();

        $insights = [];
        $totalSpend = $campaigns->sum('spend');
        $totalResults = $campaigns->sum('results');
        $avgCpr = $totalResults > 0 ? $totalSpend / $totalResults : 0;

        // Performance insights
        $highPerformers = $campaigns->where('cpr', '<', $avgCpr * 0.8)->count();
        $lowPerformers = $campaigns->where('cpr', '>', $avgCpr * 1.2)->count();

        if ($highPerformers > 0) {
            $insights[] = [
                'type' => 'success',
                'title' => 'High Performers Detected',
                'message' => "{$highPerformers} campaigns are performing 20% better than average CPR.",
                'action' => 'Consider increasing budget for these campaigns.'
            ];
        }

        if ($lowPerformers > 0) {
            $insights[] = [
                'type' => 'warning',
                'title' => 'Underperforming Campaigns',
                'message' => "{$lowPerformers} campaigns have CPR 20% higher than average.",
                'action' => 'Review targeting, creative, or consider pausing these campaigns.'
            ];
        }

        // Budget distribution insights
        $topSpenders = $campaigns->sortByDesc('spend')->take(5);
        $topSpendPercentage = $topSpenders->sum('spend') / $totalSpend * 100;

        if ($topSpendPercentage > 80) {
            $insights[] = [
                'type' => 'info',
                'title' => 'Budget Concentration',
                'message' => "Top 5 campaigns consume {$topSpendPercentage}% of total budget.",
                'action' => 'Consider diversifying budget allocation.'
            ];
        }

        return $insights;
    }

    /**
     * Get campaign comparison data
     */
    public function getCampaignComparison(string $accountId, array $campaignIds): array
    {
        $campaigns = OptimizedMonitoringCpr::forAccount($accountId)
            ->whereIn('campaign_id', $campaignIds)
            ->latest('last_synced_at')
            ->get()
            ->groupBy('campaign_id')
            ->map(function ($group) {
                return $group->first(); // Get latest record for each campaign
            });

        return $campaigns->map(function ($campaign) {
            return [
                'campaign_id' => $campaign->campaign_id,
                'campaign_name' => $campaign->campaign_name,
                'spend' => $campaign->spend,
                'results' => $campaign->results,
                'cpr' => $campaign->cpr,
                'formatted_spend' => $campaign->formatted_spent,
                'formatted_cpr' => $campaign->formatted_cpr,
                'performance_rating' => $campaign->performance_rating,
                'efficiency_score' => $this->calculateEfficiencyScore($campaign)
            ];
        })->values()->toArray();
    }

    /**
     * Calculate efficiency score for a campaign
     */
    private function calculateEfficiencyScore($campaign): float
    {
        // Efficiency score based on CPR, results, and spend
        $cprScore = $campaign->cpr > 0 ? min(100, (10 / $campaign->cpr) * 100) : 0;
        $resultsScore = min(100, $campaign->results * 2); // 2 points per result, max 100
        $spendEfficiency = $campaign->results > 0 ? min(100, ($campaign->results / $campaign->spend) * 1000) : 0;

        return round(($cprScore * 0.4 + $resultsScore * 0.3 + $spendEfficiency * 0.3), 2);
    }

    /**
     * Export data to Excel
     */
    public function exportToExcel(string $accountId, string $dateRange): string
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $campaigns = OptimizedMonitoringCpr::forAccount($accountId)
            ->inDateRange($startDate, $endDate)
            ->orderBy('spend', 'desc')
            ->get();

        $summary = $this->getPerformanceSummary($accountId, $dateRange);
        $insights = $this->getPerformanceInsights($accountId, $dateRange);

        // Create Excel file using Laravel Excel or similar
        $filename = "cpr-report-{$accountId}-" . date('Y-m-d-H-i-s') . '.xlsx';
        $filepath = storage_path("app/exports/{$filename}");

        // Ensure directory exists
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        // For now, create a CSV file (can be enhanced with Laravel Excel)
        $csvData = [];
        $csvData[] = ['Campaign Performance Report'];
        $csvData[] = ['Generated:', date('Y-m-d H:i:s')];
        $csvData[] = ['Account ID:', $accountId];
        $csvData[] = ['Date Range:', $dateRange];
        $csvData[] = [];

        // Summary
        $csvData[] = ['SUMMARY'];
        $csvData[] = ['Total Campaigns:', $summary['total_campaigns']];
        $csvData[] = ['Total Spend:', $summary['formatted_spend']];
        $csvData[] = ['Total Results:', $summary['total_results']];
        $csvData[] = ['Average CPR:', $summary['formatted_avg_cpr']];
        $csvData[] = [];

        // Campaign details
        $csvData[] = ['CAMPAIGN DETAILS'];
        $csvData[] = ['Campaign Name', 'Status', 'Spend', 'Results', 'CPR', 'Performance Rating'];

        foreach ($campaigns as $campaign) {
            $csvData[] = [
                $campaign->campaign_name,
                $campaign->status,
                $campaign->formatted_spent,
                $campaign->results,
                $campaign->formatted_cpr,
                $campaign->performance_rating
            ];
        }

        // Write CSV
        $file = fopen($filepath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        return $filepath;
    }
}
