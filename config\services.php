<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    // kirimin-aja
    'kiriminaja' => [
        'env' => env('KIRIMINAJA_ENV', 'dev'),
        'dev' => [
            'base_url' => env('KIRIMINAJA_DEV_BASE_URL'),
            'api_key' => env('KIRIMINAJA_DEV_API_KEY'),
        ],
        'prod' => [
            'base_url' => env('KIRIMINAJA_PROD_BASE_URL'),
            'api_key' => env('KIRIMINAJA_PROD_API_KEY'),
        ],
    ],

    //FACEBOOK
    'facebook' => [
        'app_id' => env('FACEBOOK_APP_ID'),
        'app_secret' => env('FACEBOOK_APP_SECRET'),
        'access_token' => env('FACEBOOK_ACCESS_TOKEN'),
        'ad_account_id' => env('FACEBOOK_AD_ACCOUNT_ID'), // Dynamic update
    ],

    // raja-ongkir
    'rajaongkir' => [
        'api_endpoint' => env('RAJAONGKIR_ENDPOINT', 'https://api-sandbox.collaborator.komerce.id'),
        'api_key' => env('RAJAONGKIR_API_KEY'),
    ],


];
