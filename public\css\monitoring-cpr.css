/* Monitoring CPR Module Styles */
.filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-active {
    background-color: #28a745;
}

.status-paused {
    background-color: #ffc107;
}

.status-inactive {
    background-color: #dc3545;
}

.btn-fetch-data {
    min-width: 150px;
    transition: all 0.2s ease;
}

.btn-fetch-data:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.8;
}

.currency-amount {
    font-weight: 600;
    color: #2c5aa0;
}

.campaign-name {
    text-align: left !important;
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Table Styles */
.table-responsive {
    margin: 0;
    border-radius: 8px;
    overflow: hidden;
}

.table tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

/* Loading States */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

/* Accessibility */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .filter-section {
        padding: 1rem;
    }

    .table-responsive {
        margin: 0 -1rem;
    }

    .btn-fetch-data {
        min-width: auto;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Toast Container */
.toast-container {
    z-index: 1050;
}

/* Form Validation */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
