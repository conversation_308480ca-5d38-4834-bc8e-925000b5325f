class MonitoringCPRDashboard {
    constructor() {
        this.accountId = null;
        this.dateRange = null;
        this.autoRefreshInterval = null;
        this.autoRefreshEnabled = false;
        this.charts = {};
        this.websocketConnection = null;
        
        this.init();
    }

    init() {
        this.initializeDatePicker();
        this.bindEvents();
        this.initializeWebSocket();
        this.updateButtonStates();
    }

    initializeDatePicker() {
        flatpickr("#dashboardDateRange", {
            mode: "range",
            dateFormat: "Y-m-d",
            defaultDate: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()],
            onChange: (selectedDates) => {
                if (selectedDates.length === 2) {
                    this.dateRange = selectedDates.map(date => 
                        date.toISOString().split('T')[0]
                    ).join(' to ');
                }
            }
        });
    }

    bindEvents() {
        // Account selection
        $("#dashboardAccountSelect").on("change", (e) => {
            this.accountId = $(e.target).val();
            this.updateButtonStates();
        });

        // Load dashboard
        $("#btnLoadDashboard").on("click", () => {
            this.loadDashboard();
        });

        // Auto refresh toggle
        $("#btnAutoRefresh").on("click", () => {
            this.toggleAutoRefresh();
        });

        // Export report
        $("#btnExportReport").on("click", () => {
            this.exportReport();
        });

        // Keyboard shortcuts
        $(document).on("keydown", (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        this.loadDashboard();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.exportReport();
                        break;
                }
            }
        });
    }

    updateButtonStates() {
        const hasAccount = !!this.accountId;
        $("#btnLoadDashboard").prop("disabled", !hasAccount);
        $("#btnExportReport").prop("disabled", !hasAccount);
    }

    async loadDashboard() {
        if (!this.accountId) {
            this.showToast("warning", "Please select an advertising account");
            return;
        }

        try {
            this.showLoadingState();
            
            const response = await $.ajax({
                url: window.dashboardRoutes.summary,
                method: "GET",
                data: {
                    idAkun: this.accountId,
                    dateRange: this.dateRange || this.getDefaultDateRange()
                }
            });

            if (response.success) {
                this.updatePerformanceCards(response.summary);
                this.updateTopPerformersTable(response.top_performers);
                this.initializeCharts(response);
                this.showDashboardSections();
                this.updateRealTimeStatus("Connected", response.last_updated);
                this.showToast("success", "Dashboard loaded successfully");
            } else {
                throw new Error(response.message || "Failed to load dashboard");
            }

        } catch (error) {
            this.handleError(error);
        } finally {
            this.hideLoadingState();
        }
    }

    updatePerformanceCards(summary) {
        $("#totalCampaigns").text(summary.total_campaigns || 0);
        $("#totalSpend").text(summary.formatted_spend || "Rp 0");
        $("#totalResults").text(summary.total_results || 0);
        $("#averageCpr").text(summary.formatted_avg_cpr || "Rp 0");
    }

    updateTopPerformersTable(topPerformers) {
        const tbody = $("#topPerformersTable tbody");
        tbody.empty();

        if (!topPerformers || topPerformers.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        No campaign data available
                    </td>
                </tr>
            `);
            return;
        }

        topPerformers.forEach((campaign, index) => {
            const performanceClass = this.getPerformanceClass(campaign.performance_rating);
            const row = `
                <tr>
                    <td><span class="badge bg-primary">#${index + 1}</span></td>
                    <td>
                        <div class="campaign-name" title="${campaign.nama_kampanye}">
                            ${campaign.nama_kampanye}
                        </div>
                    </td>
                    <td>
                        <span class="status-indicator ${this.getStatusClass(campaign.status)}"></span>
                        ${campaign.status}
                    </td>
                    <td><strong>${parseInt(campaign.hasil).toLocaleString()}</strong></td>
                    <td><span class="currency-amount">${campaign.formatted_cpr}</span></td>
                    <td><span class="currency-amount">${campaign.formatted_spent}</span></td>
                    <td>
                        <span class="badge ${performanceClass}">
                            ${campaign.performance_rating.toUpperCase()}
                        </span>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    initializeCharts(data) {
        this.initPerformanceTrendChart(data);
        this.initStatusDistributionChart(data);
    }

    initPerformanceTrendChart(data) {
        const options = {
            series: [{
                name: 'Spend',
                type: 'column',
                data: data.trend_data?.spend || []
            }, {
                name: 'Results',
                type: 'line',
                data: data.trend_data?.results || []
            }],
            chart: {
                height: 350,
                type: 'line',
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                }
            },
            stroke: {
                width: [0, 4]
            },
            title: {
                text: 'Campaign Performance Over Time'
            },
            dataLabels: {
                enabled: true,
                enabledOnSeries: [1]
            },
            labels: data.trend_data?.dates || [],
            xaxis: {
                type: 'datetime'
            },
            yaxis: [{
                title: {
                    text: 'Spend (IDR)',
                },
            }, {
                opposite: true,
                title: {
                    text: 'Results'
                }
            }]
        };

        if (this.charts.performanceTrend) {
            this.charts.performanceTrend.destroy();
        }
        
        this.charts.performanceTrend = new ApexCharts(
            document.querySelector("#performanceTrendChart"), 
            options
        );
        this.charts.performanceTrend.render();
    }

    initStatusDistributionChart(data) {
        const statusData = data.status_distribution || {};
        const series = Object.values(statusData);
        const labels = Object.keys(statusData);

        const options = {
            series: series,
            chart: {
                type: 'donut',
                height: 350
            },
            labels: labels,
            colors: ['#28a745', '#ffc107', '#dc3545', '#6c757d'],
            legend: {
                position: 'bottom'
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        if (this.charts.statusDistribution) {
            this.charts.statusDistribution.destroy();
        }

        this.charts.statusDistribution = new ApexCharts(
            document.querySelector("#statusDistributionChart"), 
            options
        );
        this.charts.statusDistribution.render();
    }

    showDashboardSections() {
        $("#performanceCards").show();
        $("#chartsSection").show();
        $("#topPerformersSection").show();
    }

    toggleAutoRefresh() {
        this.autoRefreshEnabled = !this.autoRefreshEnabled;
        
        if (this.autoRefreshEnabled) {
            this.startAutoRefresh();
            $(".auto-refresh-text").text("Auto Refresh: ON");
            $("#btnAutoRefresh").removeClass("btn-outline-primary").addClass("btn-primary");
        } else {
            this.stopAutoRefresh();
            $(".auto-refresh-text").text("Auto Refresh: OFF");
            $("#btnAutoRefresh").removeClass("btn-primary").addClass("btn-outline-primary");
        }
    }

    startAutoRefresh() {
        this.autoRefreshInterval = setInterval(() => {
            if (this.accountId) {
                this.loadDashboard();
            }
        }, 30000); // Refresh every 30 seconds
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    async exportReport() {
        if (!this.accountId) {
            this.showToast("warning", "Please select an advertising account");
            return;
        }

        try {
            const response = await $.ajax({
                url: window.dashboardRoutes.export,
                method: "POST",
                data: {
                    idAkun: this.accountId,
                    dateRange: this.dateRange || this.getDefaultDateRange(),
                    _token: window.csrfToken
                },
                xhrFields: {
                    responseType: 'blob'
                }
            });

            // Create download link
            const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cpr-report-${this.accountId}-${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showToast("success", "Report exported successfully");

        } catch (error) {
            this.handleError(error);
        }
    }

    initializeWebSocket() {
        // Initialize WebSocket connection for real-time updates
        if (window.Echo) {
            this.websocketConnection = window.Echo.channel('cpr-monitoring')
                .listen('CprDataUpdated', (e) => {
                    if (e.accountId === this.accountId) {
                        this.handleRealTimeUpdate(e.data);
                    }
                });
        }
    }

    handleRealTimeUpdate(data) {
        this.updateRealTimeStatus("Updated", new Date().toLocaleString());
        if (this.autoRefreshEnabled) {
            this.loadDashboard();
        } else {
            this.showToast("info", "New data available. Click refresh to update.");
        }
    }

    updateRealTimeStatus(status, lastUpdate) {
        $("#realTimeStatus").removeClass("d-none");
        $("#statusText").text(status);
        $("#lastUpdate").text(`Last update: ${lastUpdate}`);
        
        setTimeout(() => {
            $("#realTimeStatus").addClass("d-none");
        }, 5000);
    }

    getDefaultDateRange() {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        return firstDay.toISOString().split('T')[0] + ' to ' + now.toISOString().split('T')[0];
    }

    getPerformanceClass(rating) {
        switch(rating) {
            case 'excellent': return 'bg-success';
            case 'good': return 'bg-primary';
            case 'average': return 'bg-warning';
            case 'poor': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    getStatusClass(status) {
        switch(status?.toLowerCase()) {
            case 'active': return 'status-active';
            case 'paused': return 'status-paused';
            default: return 'status-inactive';
        }
    }

    showLoadingState() {
        $("#btnLoadDashboard").prop("disabled", true).html('<i class="bx bx-loader-alt bx-spin me-1"></i>Loading...');
    }

    hideLoadingState() {
        $("#btnLoadDashboard").prop("disabled", false).html('<i class="bx bx-chart-bar me-1"></i>Load Dashboard');
    }

    handleError(error) {
        console.error("Dashboard error:", error);
        this.showToast("error", error.message || "An error occurred while loading the dashboard");
    }

    showToast(type, message) {
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
        });

        Toast.fire({
            icon: type,
            title: message,
        });
    }
}

// Initialize the dashboard
$(document).ready(() => {
    new MonitoringCPRDashboard();
});
