class MonitoringCPR {
    constructor() {
        this.table = null;
        this.filters = {
            accountId: null,
        };
        this.cache = {
            data: null,
            timestamp: null,
            expiry: 5 * 60 * 1000, // 5 minutes
        };
        this.init();
    }

    init() {
        // Verify routes are available
        if (!this.verifyRoutes()) {
            this.showToast(
                "error",
                "Application configuration error. Please contact support."
            );
            return;
        }

        this.initializeDataTable();
        this.bindEvents();
        this.updateButtonState();
        this.addInitialStateGuidance();
    }

    verifyRoutes() {
        const requiredRoutes = [
            "monitoringCprData",
            "monitoringCprCreate",
            "monitoringCprSync",
        ];
        return requiredRoutes.every((route) => window.routes[route]);
    }

    addInitialStateGuidance() {
        const guidance = `
            <div class="alert alert-info mb-3" role="alert">
                <i class="bx bx-info-circle me-2"></i>
                Please select an advertising account to begin monitoring campaign performance.
            </div>
        `;
        $(".filter-section").prepend(guidance);
    }

    initializeDataTable() {
        if (!window.routes.monitoringCprData) {
            console.error("Data route not defined");
            return;
        }

        // Destroy existing table if it exists
        if ($.fn.DataTable.isDataTable("#tabelMasterCampaign")) {
            $("#tabelMasterCampaign").DataTable().destroy();
        }

        this.table = $("#tabelMasterCampaign").DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: window.routes.monitoringCprData,
                type: "GET",
                data: (d) => {
                    d.idAkun = this.filters.accountId;
                    console.log("Fetching data with params:", d);
                    return d;
                },
                dataSrc: (response) => {
                    console.log("Received data:", response);
                    if (!response.data) {
                        console.error("No data received from server");
                        return [];
                    }
                    return response.data;
                },
                error: (xhr, error, thrown) => {
                    console.error("DataTable error:", error, thrown);
                    this.handleError(error, "loadData");
                },
            },
            columns: [
                {
                    data: null,
                    name: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                    width: "50px",
                    render: function (data, type, row, meta) {
                        return meta.row + meta.settings._iDisplayStart + 1;
                    },
                },
                {
                    data: "campaign_name",
                    name: "campaign_name",
                    render: (data) => {
                        if (!data) return '<span class="text-muted">-</span>';
                        return `<div class="campaign-name" title="${data}">${data}</div>`;
                    },
                },
                {
                    data: "status",
                    name: "status",
                    render: (data) => {
                        if (!data) return '<span class="text-muted">-</span>';
                        const statusClass = this.getStatusClass(
                            data.toLowerCase()
                        );
                        return `<span class="status-indicator ${statusClass}"></span>${data}`;
                    },
                },
                {
                    data: "results",
                    name: "results",
                    render: (data) =>
                        data
                            ? `<strong>${parseInt(
                                  data
                              ).toLocaleString()}</strong>`
                            : '<span class="text-muted">0</span>',
                },
                {
                    data: "cpr",
                    name: "cpr",
                    render: (data) => {
                        if (!data || data === "-")
                            return '<span class="text-muted">-</span>';
                        return `<span class="currency-amount">${data}</span>`;
                    },
                },
                {
                    data: "spend",
                    name: "spend",
                    render: (data) => {
                        if (!data || data === "-")
                            return '<span class="text-muted">-</span>';
                        return `<span class="currency-amount">${data}</span>`;
                    },
                },
            ],
            order: [[0, "asc"]],
            pageLength: 25,
            lengthMenu: [
                [10, 25, 50, 100],
                [10, 25, 50, 100],
            ],
            language: {
                processing:
                    '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
                emptyTable:
                    'No campaign data available. Click "Sync with Facebook" to load from Facebook Ads.',
                zeroRecords: "No campaigns found for the selected criteria.",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "Showing 0 to 0 of 0 entries",
                infoFiltered: "(filtered from _MAX_ total entries)",
                lengthMenu: "Show _MENU_ entries",
                search: "Search:",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous",
                },
            },
            drawCallback: (settings) => {
                console.log("Table redrawn with data:", settings.json);
                this.updateTableAccessibility();
            },
            initComplete: (settings, json) => {
                console.log("Table initialization complete:", json);
            },
        });

        // Add error handling for AJAX requests
        $(document).ajaxError((event, jqXHR, settings, error) => {
            console.error("AJAX Error:", {
                url: settings.url,
                status: jqXHR.status,
                error: error,
            });
        });
    }

    updateTableAccessibility() {
        // Add ARIA labels and roles
        $("#tabelMasterCampaign").attr("role", "grid");
        $("#tabelMasterCampaign thead").attr("role", "rowgroup");
        $("#tabelMasterCampaign tbody").attr("role", "rowgroup");
        $("#tabelMasterCampaign th").attr("role", "columnheader");
        $("#tabelMasterCampaign td").attr("role", "cell");
    }

    bindEvents() {
        // Account selection
        $("#idAkun").on("change", (e) => {
            this.filters.accountId = $(e.target).val();
            this.updateButtonState();
            this.clearValidationErrors();
            if (this.table) {
                this.table.draw();
            }
        });

        // Fetch data button
        $("#btnTambahCpr").on("click", (e) => {
            e.preventDefault();
            this.fetchLatestData();
        });

        // Sync data button
        $("#btnSyncData").on("click", (e) => {
            e.preventDefault();
            this.syncWithFacebook();
        });

        // Refresh table button
        $("#btnRefreshTable").on("click", (e) => {
            e.preventDefault();
            if (this.filters.accountId && this.table) {
                this.table.draw();
                this.showToast("info", "Table refreshed");
            }
        });

        // Add keyboard navigation
        this.addKeyboardNavigation();
    }

    addKeyboardNavigation() {
        $(document).on("keydown", (e) => {
            if (e.ctrlKey && e.key === "r") {
                e.preventDefault();
                $("#btnRefreshTable").click();
            }
        });
    }

    updateButtonState() {
        const hasAccount = !!this.filters.accountId;
        $("#btnTambahCpr").prop("disabled", !hasAccount);
        $("#btnSyncData").prop("disabled", !hasAccount);
        $("#btnRefreshTable").prop("disabled", !hasAccount);
    }

    async fetchLatestData() {
        if (!this.validateForm()) return;
        if (!window.routes.monitoringCprCreate) {
            this.showToast("error", "Create route not defined");
            return;
        }

        const $btn = $("#btnTambahCpr");
        const originalText = $btn.find(".btn-text").text();

        try {
            this.setLoadingState($btn, "Fetching...");

            const accountId = this.filters.accountId;
            const accountName = $("#idAkun option:selected").text();

            const response = await $.ajax({
                url: window.routes.monitoringCprCreate,
                method: "GET",
                data: {
                    idAkun: accountId,
                    namaAkun: accountName,
                },
                error: (xhr, status, error) => {
                    throw new Error(xhr.responseJSON?.message || error);
                },
            });

            if (this.table) {
                this.table.draw();
            }
            this.showToast("success", "Latest data fetched successfully");
            this.updateCache(response);
        } catch (error) {
            this.handleError(error, "fetchLatestData");
        } finally {
            this.resetButtonState($btn, originalText);
        }
    }

    async syncWithFacebook() {
        if (!this.validateForm()) return;
        if (!window.routes.monitoringCprSync) {
            this.showToast("error", "Sync route not defined");
            return;
        }

        const $btn = $("#btnSyncData");
        const originalText = $btn.find(".btn-text").text();

        try {
            this.setLoadingState($btn, "Syncing...");

            const accountId = this.filters.accountId;
            const accountName = $("#idAkun option:selected").text();

            const response = await $.ajax({
                url: window.routes.monitoringCprSync,
                method: "POST",
                data: {
                    idAkun: accountId,
                    namaAkun: accountName,
                    _token: window.csrfToken,
                },
                error: (xhr, status, error) => {
                    throw new Error(xhr.responseJSON?.message || error);
                },
            });

            if (this.table) {
                this.table.draw();
            }
            this.showToast(
                "success",
                "Data synchronized successfully with Facebook"
            );
            this.updateCache(response);
        } catch (error) {
            this.handleError(error, "syncWithFacebook");
        } finally {
            this.resetButtonState($btn, originalText);
        }
    }

    setLoadingState($btn, text) {
        $btn.prop("disabled", true)
            .addClass("btn-loading")
            .find(".btn-text")
            .text(text);
        $btn.find("i").addClass("bx-spin");
    }

    resetButtonState($btn, originalText) {
        $btn.prop("disabled", false)
            .removeClass("btn-loading")
            .find(".btn-text")
            .text(originalText);
        $btn.find("i").removeClass("bx-spin");
    }

    validateForm() {
        const accountId = this.filters.accountId;
        const errors = [];

        if (!accountId) {
            errors.push({
                field: "#idAkun",
                message: "Please select an advertising account",
            });
        }

        if (errors.length > 0) {
            errors.forEach((error) => {
                this.showValidationError(error.field, error.message);
            });
            return false;
        }

        this.clearValidationErrors();
        return true;
    }

    showValidationError(selector, message) {
        $(selector)
            .addClass("is-invalid")
            .siblings(".invalid-feedback")
            .html(message);
    }

    clearValidationErrors() {
        $(".is-invalid").removeClass("is-invalid");
        $(".invalid-feedback").html("");
    }

    getStatusClass(status) {
        switch (status) {
            case "active":
                return "status-active";
            case "paused":
                return "status-paused";
            default:
                return "status-inactive";
        }
    }

    handleError(error, operation) {
        const message = error.message || "An error occurred. Please try again.";

        Swal.fire({
            title: "Operation Failed",
            text: message,
            icon: "error",
            showCancelButton: true,
            confirmButtonText: "Retry",
            cancelButtonText: "Close",
        }).then((result) => {
            if (result.isConfirmed) {
                this[operation]();
            }
        });
    }

    showToast(type, message) {
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
        });

        Toast.fire({
            icon: type,
            title: message,
        });
    }

    updateCache(data) {
        this.cache.data = data;
        this.cache.timestamp = Date.now();
    }

    isCacheValid() {
        return (
            this.cache.data &&
            this.cache.timestamp &&
            Date.now() - this.cache.timestamp < this.cache.expiry
        );
    }
}

// Initialize the application
$(document).ready(() => {
    new MonitoringCPR();
});
