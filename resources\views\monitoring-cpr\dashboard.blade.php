@extends('templates.app')
@section('title', $title)
@section('content')
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
            <small class="text-muted">Real-time Facebook Ads campaign performance analytics</small>
        </div>
        <div class="d-flex gap-2">
            <button id="btnAutoRefresh" class="btn btn-outline-primary btn-sm">
                <i class="bx bx-refresh me-1"></i>
                <span class="auto-refresh-text">Auto Refresh: OFF</span>
            </button>
            <button id="btnExportReport" class="btn btn-success btn-sm">
                <i class="bx bx-download me-1"></i>Export Report
            </button>
        </div>
    </div>
    <hr />

    <!-- Account Selection -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="dashboardAccountSelect" class="form-label">
                        Advertising Account <span class="text-danger">*</span>
                    </label>
                    <select class="form-select" id="dashboardAccountSelect" name="dashboardAccountSelect">
                        <option value="">Choose an account...</option>
                        @foreach ($akun as $a)
                            <option value="{{ $a['id'] }}">{{ $a['nama'] }} ({{ $a['id'] }})</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="dashboardDateRange" class="form-label">Date Range</label>
                    <input type="text" id="dashboardDateRange" class="form-control" 
                           value="{{ date('Y-m-01') . ' to ' . date('Y-m-t') }}" 
                           placeholder="Select Date Range">
                </div>
                <div class="col-md-4">
                    <button id="btnLoadDashboard" class="btn btn-primary" disabled>
                        <i class="bx bx-chart-bar me-1"></i>Load Dashboard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Summary Cards -->
    <div class="row mb-4" id="performanceCards" style="display: none;">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white">Total Campaigns</h6>
                            <h4 class="mb-0 text-white" id="totalCampaigns">0</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="bx bx-chart-bar font-size-40"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white">Total Spend</h6>
                            <h4 class="mb-0 text-white" id="totalSpend">Rp 0</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="bx bx-money font-size-40"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white">Total Results</h6>
                            <h4 class="mb-0 text-white" id="totalResults">0</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="bx bx-target-lock font-size-40"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white">Average CPR</h6>
                            <h4 class="mb-0 text-white" id="averageCpr">Rp 0</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="bx bx-calculator font-size-40"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4" id="chartsSection" style="display: none;">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bx bx-line-chart me-2"></i>Campaign Performance Trends
                    </h6>
                </div>
                <div class="card-body">
                    <div id="performanceTrendChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bx bx-pie-chart me-2"></i>Campaign Status Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <div id="statusDistributionChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers Table -->
    <div class="card" id="topPerformersSection" style="display: none;">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bx bx-trophy me-2"></i>Top Performing Campaigns
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="topPerformersTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Campaign Name</th>
                            <th>Status</th>
                            <th>Results</th>
                            <th>CPR</th>
                            <th>Amount Spent</th>
                            <th>Performance Rating</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Real-time Status Indicator -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
        <div id="realTimeStatus" class="alert alert-info d-none" role="alert">
            <i class="bx bx-wifi me-2"></i>
            <span id="statusText">Connected</span>
            <small id="lastUpdate" class="d-block"></small>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer"></div>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="{{ asset('css/monitoring-cpr-dashboard.css') }}">
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // Define routes for JavaScript
        window.dashboardRoutes = {
            summary: "{{ route('ads.monitoring-cpr.summary') }}",
            export: "{{ route('ads.monitoring-cpr.export') }}",
            realTimeData: "{{ route('ads.monitoring-cpr.real-time') }}"
        };

        // Define CSRF token
        window.csrfToken = "{{ csrf_token() }}";
    </script>
    <script src="{{ asset('js/monitoring-cpr-dashboard.js') }}"></script>
@endpush
