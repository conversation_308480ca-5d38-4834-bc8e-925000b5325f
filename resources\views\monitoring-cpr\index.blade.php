@extends('templates.app')
@section('title', $title)
@section('content')
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
            <small class="text-muted">Monitor and analyze Facebook Ads campaign performance</small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('ads.monitoring-cpr.dashboard') }}" class="btn btn-primary btn-sm">
                <i class="bx bx-chart-bar me-1"></i>Dashboard
            </a>
            <button id="btnBulkActions" class="btn btn-outline-secondary btn-sm dropdown-toggle"
                    data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bx bx-cog me-1"></i>Actions
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" id="btnBulkSync">
                        <i class="bx bx-sync me-2"></i>Bulk Sync All Accounts
                    </a></li>
                <li><a class="dropdown-item" href="#" id="btnExportAll">
                        <i class="bx bx-download me-2"></i>Export All Data
                    </a></li>
                <li>
                    <hr class="dropdown-divider">
                </li>
                <li><a class="dropdown-item" href="#" id="btnSettings">
                        <i class="bx bx-cog me-2"></i>Settings
                    </a></li>
            </ul>
        </div>
    </div>
    <hr />

    <!-- Filter Section -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bx bx-filter me-2"></i>Filters & Data Management
            </h6>
            <small class="text-muted">Select an advertising account and date range to view CPR data</small>
        </div>
        <div class="card-body filter-section">
            @if (empty($akun) || count($akun) === 0)
                <div class="alert alert-warning" role="alert">
                    <i class="bx bx-info-circle me-2"></i>
                    <strong>No advertising accounts available.</strong>
                    Please ensure Facebook API is configured and you have access to advertising accounts.
                </div>
            @else
                <div class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="idAkun" class="form-label">
                            Advertising Account <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="idAkun" name="idAkun" required aria-label="Select advertising account">
                            <option value="">Choose an account...</option>
                            @foreach ($akun as $a)
                                <option value="{{ $a['id'] }}">{{ $a['nama'] }} ({{ $a['id'] }})</option>
                            @endforeach
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-8">
                        <button id="btnSyncData" class="btn btn-primary" disabled aria-label="Sync data with Facebook">
                            <i class="bx bx-sync me-1" aria-hidden="true"></i>
                            <span class="btn-text">Sync Data</span>
                        </button>
                        <button id="btnRefreshTable" class="btn btn-outline-secondary ms-2" disabled aria-label="Refresh table data">
                            <i class="bx bx-refresh" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Data Table Section -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bx bx-chart-bar me-2"></i>Campaign Performance Data
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="tabelMasterCampaign" class="table datatable table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th width="50">No</th>
                            <th width="300">Campaign Name</th>
                            <th width="100">Status</th>
                            <th width="100">Results</th>
                            <th width="150">CPR (Cost per Result)</th>
                            <th width="150">Amount Spent</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer"></div>

    <!-- Modal Container -->
    <div id="modalContainer"></div>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="{{ asset('css/monitoring-cpr.css') }}">
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Define routes for JavaScript
        window.routes = {
            monitoringCprData: "{{ route('ads.monitoring-cpr.data') }}",
            monitoringCprCreate: "{{ route('ads.monitoring-cpr.create') }}",
            monitoringCprSync: "{{ route('ads.monitoring-cpr.sync') }}"
        };

        // Define CSRF token
        window.csrfToken = "{{ csrf_token() }}";

        // Add error handling for missing routes
        Object.entries(window.routes).forEach(([key, value]) => {
            if (!value) {
                console.error(`Route ${key} is not defined`);
            }
        });

        // Log routes for debugging
        console.log('Routes:', window.routes);
        console.log('CSRF Token:', window.csrfToken);
    </script>
    <script src="{{ asset('js/monitoring-cpr.js') }}"></script>
@endpush
