<style>
    #tabelDetailCampaign {
        font-size: 12px;
    }

    #tabelDetailCampaign thead th {
        font-size: 11px;
        background-color: #f8f9fa;
        font-weight: 600;
    }

    #tabelDetailCampaign tbody td {
        font-size: 11px;
        vertical-align: middle;
    }

    .campaign-details-modal .modal-dialog {
        max-width: 95%;
    }

    .currency-amount {
        font-weight: 600;
        color: #2c5aa0;
    }

    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
    }

    .status-active {
        background-color: #28a745;
    }

    .status-paused {
        background-color: #ffc107;
    }

    .status-inactive {
        background-color: #dc3545;
    }

    .exchange-rate-info {
        font-size: 10px;
        color: #6c757d;
        margin-top: 5px;
    }
</style>

<div class="modal fade campaign-details-modal" id="modalCreateCampaign" tabindex="-1" aria-labelledby="modalCreateCampaignLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <div>
                    <h5 class="modal-title mb-0" id="modalCreateCampaignLabel">
                        <i class="bx bx-chart-bar me-2"></i>Campaign Data: {{ $namaAkun }}
                    </h5>
                    <small class="text-white-50">Fresh data from Facebook Ads API</small>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info d-flex align-items-center" role="alert">
                    <i class="bx bx-info-circle me-2"></i>
                    <div>
                        <strong>Data successfully synced from Facebook Ads!</strong>
                        The table below shows the latest campaign performance data.
                        <div class="exchange-rate-info">
                            Exchange rate: 1 PHP = {{ number_format($exchangeRate ?? 278.62, 2) }} IDR
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="tabelDetailCampaign" class="table table-striped table-hover" style="width:100%">
                        <thead>
                            <tr>
                                <th width="50">No</th>
                                <th width="250">Campaign Name</th>
                                <th width="100">Status</th>
                                <th width="80">Results</th>
                                <th width="120">CPR (Cost per Result)</th>
                                <th width="120">Amount Spent</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="text-muted small">
                        <i class="bx bx-time me-1"></i>
                        Data updated: {{ now()->format('Y-m-d H:i:s') }}
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bx bx-x me-1"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        let campaign = @json($campaign);
        const exchangeRate = {{ $exchangeRate ?? 278.62 }};

        // Add manual row index for DT_RowIndex column
        campaign = campaign.map((item, index) => ({
            ...item,
            DT_RowIndex: index + 1
        }));

        // Helper functions
        const formatRupiah = (amount) => {
            if (!amount || amount === 0) return '-';
            return 'Rp ' + parseInt(amount).toLocaleString('id-ID');
        };

        const getStatusClass = (status) => {
            if (!status) return 'status-inactive';
            const statusLower = status.toLowerCase();
            switch (statusLower) {
                case 'active':
                    return 'status-active';
                case 'paused':
                    return 'status-paused';
                default:
                    return 'status-inactive';
            }
        };

        const renderStatus = (status) => {
            if (!status) return '<span class="text-muted">Unknown</span>';
            const statusClass = getStatusClass(status);
            return `<span class="status-indicator ${statusClass}"></span>${status}`;
        };

        const renderCampaignName = (name) => {
            if (!name) return '<span class="text-muted">-</span>';
            const truncated = name.length > 30 ? name.substring(0, 30) + '...' : name;
            return `<div title="${name}" style="text-align:left;">${truncated}</div>`;
        };

        let table = $('#tabelDetailCampaign').DataTable({
            data: campaign,
            pageLength: 10,
            responsive: true,
            language: {
                search: "Search campaigns:",
                lengthMenu: "Show _MENU_ campaigns per page",
                info: "Showing _START_ to _END_ of _TOTAL_ campaigns",
                emptyTable: "No campaign data available",
                zeroRecords: "No campaigns found matching your search"
            },
            columns: [{
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    orderable: false,
                    searchable: false,
                    className: 'text-center'
                },
                {
                    data: 'nama_kampanye',
                    render: (data) => renderCampaignName(data)
                },
                {
                    data: 'status',
                    render: (data) => renderStatus(data),
                    className: 'text-center'
                },
                {
                    data: 'hasil',
                    render: (data) => {
                        if (!data || data === 0) return '<span class="text-muted">0</span>';
                        return `<strong>${parseInt(data).toLocaleString()}</strong>`;
                    },
                    className: 'text-center'
                },
                {
                    data: 'cpr',
                    render: (data) => {
                        if (!data || data === 0) return '<span class="text-muted">-</span>';
                        const pesoValue = parseFloat(data);
                        const rupiah = pesoValue * exchangeRate;
                        return `<span class="currency-amount">${formatRupiah(rupiah)}</span>`;
                    },
                    className: 'text-end'
                },
                {
                    data: 'jumlah_dibelanjakan',
                    render: (data) => {
                        if (!data || data === 0) return '<span class="text-muted">-</span>';
                        const pesoValue = parseFloat(data);
                        const rupiah = pesoValue * exchangeRate;
                        return `<span class="currency-amount">${formatRupiah(rupiah)}</span>`;
                    },
                    className: 'text-end'
                }
            ],
            order: [
                [1, 'asc']
            ], // Sort by campaign name
            drawCallback: function() {
                // Add tooltips to truncated campaign names
                $('[title]').tooltip();
            }
        });

        // Show a success message when modal opens
        $('#modalCreateCampaign').on('shown.bs.modal', function() {
            // Optional: Show toast notification
            if (typeof showToast === 'function') {
                showToast('success', `Loaded ${campaign.length} campaigns successfully`);
            }
        });

        // Cleanup tooltips when modal closes
        $('#modalCreateCampaign').on('hidden.bs.modal', function() {
            $('[title]').tooltip('dispose');
        });
    });
</script>
