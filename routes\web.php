<?php

use App\Http\Controllers\LandingPageController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\StokController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ReturController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\StokReturController;
use App\Http\Controllers\BiayaIklanController;
use App\Http\Controllers\FacebookAdController;
use App\Http\Controllers\FacebookPixelController;
use App\Http\Controllers\PaymentADVController;
use App\Http\Controllers\MateriIklanController;
use App\Http\Controllers\RatioPerAdvController;
use App\Http\Controllers\MonitoringCprController;
use App\Http\Controllers\RatioPerOrderController;
use App\Http\Controllers\RatioPerProdukController;
use App\Http\Controllers\RekapPaymentCSController;
use App\Http\Controllers\EarningPotentialController;
use App\Http\Controllers\MonitoringPengirimanController;
use App\Http\Controllers\RekapMetodePembayaranController;
use App\Http\Controllers\CheckoutProdukController;
use App\Http\Controllers\ApiKeyController;

use App\Http\Controllers\DataMaster\PegawaiController;
use App\Http\Controllers\DataMaster\ProductController;
use App\Http\Controllers\DataMaster\BiayaIklanController as DataMasterBiayaIklanController;
use App\Http\Controllers\DataMaster\EarningPotentialController as DataMasterEarningPotentialController;
use App\Http\Controllers\DataMaster\MasterPerhitunganController;

use App\Http\Controllers\KiriminAja\KurirController;
use App\Http\Controllers\KiriminAja\CronWilayahController;
use App\Http\Controllers\KiriminAja\WilayahController;

use App\Http\Controllers\Laporan\FeeGudangTFController;
use App\Http\Controllers\Laporan\LaporanStokController;
use App\Http\Controllers\Laporan\FeeGudangCODController;
use App\Http\Controllers\Laporan\LaporanReturController;
use App\Http\Controllers\Laporan\ListKulakController;
use App\Http\Controllers\Laporan\FeeGudangReturController;
use App\Http\Controllers\Laporan\LaporanCashBackController;
use App\Http\Controllers\Laporan\LaporanPaymentCSController;
use App\Http\Controllers\Laporan\LaporanPaymentADVController;
use App\Http\Controllers\Laporan\LaporanPengirimanController;
use App\Http\Controllers\Laporan\LaporanBarangRusakController;
use App\Http\Controllers\Laporan\LaporanPerformaIklanController;



// Temporary route for local environment
// if (app()->environment('production')) {
Route::get('routes', function () {
    $routeCollection = Route::getRoutes()->getRoutes();

    echo "<table border='1' style='width:100%; border-collapse: collapse;'>";
    echo "<tr style='background-color: #f2f2f2;'>";
    echo "<th width='10%'>HTTP Method</th>";
    echo "<th width='30%'>Route</th>";
    echo "<th width='20%'>Name</th>";
    echo "<th width='40%'>Corresponding Action</th>";
    echo "</tr>";

    foreach ($routeCollection as $route) {
        echo "<tr>";
        echo "<td>" . implode(', ', $route->methods()) . "</td>";
        echo "<td>" . $route->uri() . "</td>";
        echo "<td>" . ($route->getActionName() ?? 'Closure') . "</td>";
        echo "<td>" . ($route->getActionName() !== 'Closure' ? $route->getActionName() : 'Closure') . "</td>";
        echo "</tr>";
    }

    echo "</table>";
});
// }
Route::get('/test-broadcast', function () {
    event(new \App\Events\OrderCreated('halo', 'Halo dari Laravel!'));
    return 'Event dikirim';
});

// Checkout route
Route::get('/produk/{slug}', [CheckoutProdukController::class, 'index'])->name('checkout.index');

// Landing page public route
Route::get('/landing/{slug}', [LandingPageController::class, 'show'])->name('landing.show');

// CRON JOB KIRIMINAJA
Route::get('/cron-province', [CronWilayahController::class, 'getProvinces'])->name('cron.province');
Route::get('/cron-cities', [CronWilayahController::class, 'syncCities'])->name('cron.cities');
Route::get('/cron-districts', [CronWilayahController::class, 'syncDistricts'])->name('cron.districts');
// Authentication routes
Route::get('/login', [AuthController::class, 'login'])->name('login');
Route::post('/doLogin', [AuthController::class, 'doLogin'])->name('doLogin');
Route::get('logout', [AuthController::class, 'logout'])->name('logout');

// Error route
Route::get('/403', function () {
    return view('errors.403');
})->name('403');

Route::get('/getAdReport', [FacebookAdController::class, 'adReport']);
Route::get('/campaigns', [FacebookAdController::class, 'getDataCampaigns']);
Route::get('get-provinsi', [WilayahController::class, 'getProvinces'])->name('get-provinsi');
Route::get('get-kabupaten/{id}', [WilayahController::class, 'getCities'])->name('get-kabupaten');
Route::get('get-kecamatan/{id}', [WilayahController::class, 'getDistricts'])->name('get-kecamatan');
Route::get('get-kelurahan/{id}', [WilayahController::class, 'getSubDistricts'])->name('get-kelurahan');
Route::post('get-courier', [KurirController::class, 'getCurier'])->name('getCurier');

// Middleware groups
Route::group(['middleware' => 'XSS'], function () {
    Route::group(['middleware' => 'auth'], function () {
        // Route::get('/', function () {
        //     return view('dashboard');
        // })->name('dashboard');
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
        Route::prefix('dashboard')->name('dashboard.')->group(function () {
            Route::get('/getDataBarChart', [DashboardController::class, 'getDataBarChart'])->name('getDataBarChart');
            Route::get('/getDataPieChart', [DashboardController::class, 'getDataPieChart'])->name('getDataPieChart');
        });

        // Data Master routes
        Route::prefix('data-master')->name('data-master.')->group(function () {
            Route::resourceWithData('pegawai', PegawaiController::class, ['except' => ['show']]);
            Route::resourceWithData('perhitungan', MasterPerhitunganController::class, ['except' => ['show']]);
            Route::resourceWithData('biaya-iklan', DataMasterBiayaIklanController::class, ['except' => ['show']]);
            Route::resourceWithData('product', ProductController::class);

            Route::resourceWithData('earning-potential', DataMasterEarningPotentialController::class);
            Route::post('earning-potential/update', [DataMasterEarningPotentialController::class, 'update'])->name('earning-potential.update');
            Route::get('getData', [DataMasterEarningPotentialController::class, 'getData'])->name(name: 'earning-potential.getData');
        });

        // API Key Configuration routes (Admin only)
        Route::prefix('api-keys')->name('api-keys.')->group(function () {
            Route::get('/', [ApiKeyController::class, 'index'])->name('index');
            Route::put('update', [ApiKeyController::class, 'update'])->name('update');
            Route::post('test-connection', [ApiKeyController::class, 'testConnection'])->name('test-connection');
        });

        Route::group([], function () {
            Route::resourceWithData('order', OrderController::class, ['except' => ['show']]);
            Route::get('order/view-import', [OrderController::class, 'viewImportExcel'])->name('order.view-import');
            Route::post('order/import', [OrderController::class, 'importExcel'])->name('order.import');
            Route::get('form-create-resi', [OrderController::class, 'formCreateResi'])->name('order.form-create-resi');
            Route::post('order/create-resi', [OrderController::class, 'createResi'])->name('order.create-resi');
            Route::post('order/update-status/{order}', [OrderController::class, 'updateStatus'])->name('order.update-status');
            Route::post('order/update-payment-status/{order}', [OrderController::class, 'updatePaymentStatus'])->name('order.update-payment-status');
            Route::post('/order/bulk-update', [OrderController::class, 'bulkUpdate'])->name('order.bulk-update');
            Route::get('/order/cetak/resi', [OrderController::class, 'cetakResi'])->name('order.cetak-resi');

            #silvi add post detail satuan ongkir cod dan tf
            Route::get('form-create-satuan-ongkir', [OrderController::class, 'formCreateSatuanOngkir'])->name('order.form-create-satuan-ongkir');
            Route::post('order/create-satuan-ongkir', [OrderController::class, 'createSatuanOngkir'])->name('order.create-satuan-ongkir');
            Route::post('order/upload-bukti-pembayaran', [OrderController::class, 'storeUploadBuktiPembayaran'])->name('order.upload-bukti-pembayaran');
            Route::post('order/follow-up', [OrderController::class, 'followUp'])->name('order.follow-up');
        });

        Route::resourceWithData('payment-adv', PaymentADVController::class, ['except' => ['show', 'update']]);
        Route::post('payment-adv/update', [PaymentADVController::class, 'update'])->name('payment-adv.update');
        Route::get('minus-values', [PaymentAdvController::class, 'getMinusValues'])->name('payment-adv.getMinusValues');
        Route::post('update-minus', [PaymentAdvController::class, 'updateMinus'])->name('payment-adv.updateMinus');

        Route::prefix('ratio')->name('ratio.')->group(function () {
            Route::resourceWithData('ratio-per-order', RatioPerOrderController::class, ['except' => ['show']]);
            Route::resourceWithData('ratio-per-produk', RatioPerProdukController::class, ['except' => ['show']]);
            Route::resourceWithData('ratio-per-adv', RatioPerAdvController::class, ['except' => ['show']]);
        });

        Route::prefix('rekap')->name('rekap.')->group(function () {
            Route::resourceWithData('metode-pembayaran', RekapMetodePembayaranController::class, ['except' => ['show']]);
        });

        Route::prefix('ads')->name('ads.')->group(function () {
            Route::resourceWithData('materi-iklan', MateriIklanController::class, ['except' => ['show']]);
            Route::post('materi-iklan/update-status/{id}', [MateriIklanController::class, 'updateStatus'])->name('materi-iklan.update-status');

            Route::resourceWithData('earning-potential', EarningPotentialController::class, ['except' => ['show']]);
            Route::resourceWithData('monitoring-cpr', MonitoringCprController::class, ['except' => ['show']]);

            // Additional optimized routes for monitoring CPR
            Route::prefix('monitoring-cpr')->name('monitoring-cpr.')->group(function () {
                Route::get('summary', [MonitoringCprController::class, 'summary'])->name('summary');
                Route::post('refresh', [MonitoringCprController::class, 'refresh'])->name('refresh');
                Route::post('cleanup', [MonitoringCprController::class, 'cleanup'])->name('cleanup');
                Route::post('sync', [MonitoringCprController::class, 'sync'])->name('sync');
            });

            Route::resourceWithData('biaya-iklan', BiayaIklanController::class, ['except' => ['show']]);
        });

        Route::prefix('gudang')->name('gudang.')->group(function () {
            Route::resourceWithData('retur', ReturController::class, ['except' => ['show']]);
            Route::resourceWithData('stok-retur', StokReturController::class, ['except' => ['show']]);
            Route::post('stok-retur/{id}/update-status', [StokReturController::class, 'updateStatus'])->name('stok-retur.update-status');
            Route::post('stok-retur/update-status-bulk', [StokReturController::class, 'updateStatusBulk'])->name('stok-retur.update-status-bulk');
            Route::post('stok-retur/reset-status-bulk', [StokReturController::class, 'resetStatusBulk'])
                ->name('stok-retur.reset-status-bulk');
            Route::post('stok-retur/kirim-ulang', [StokReturController::class, 'kirimUlang'])->name('stok-retur.kirim-ulang');
        });

        Route::resourceWithData('stok', StokController::class, ['except' => ['show']]);

        Route::prefix('laporan')->name('laporan.')->group(function () {
            Route::resourceWithData('fee-gudang-cod', FeeGudangCODController::class, ['except' => ['show']]);
            Route::resourceWithData('fee-gudang-tf', FeeGudangTFController::class, ['except' => ['show']]);

            Route::resourceWithData('fee-gudang-retur', FeeGudangReturController::class, ['except' => ['show']]);
            Route::resourceWithData('list-kulak', ListKulakController::class, ['except' => ['show']]);
            Route::get('/list-kulak/lacak/{id}', [ListKulakController::class, 'lacak'])->name('list-kulak.lacak');
            Route::get('/list-kulak/product/{id}', [ListKulakController::class, 'getProductDetails'])->name('list-kulak.product-details');

            Route::resourceWithData('barang-rusak', LaporanBarangRusakController::class, ['except' => ['show']]);
            Route::resourceWithData('stok', LaporanStokController::class, ['except' => ['show']]);
            Route::resourceWithData('payment-cs', LaporanPaymentCSController::class, ['except' => ['show']]);
            Route::get('/payment-cs/get-data-by-sales', [LaporanPaymentCSController::class, 'getDataBySales'])->name('getDataBySales');

            Route::resourceWithData('retur', LaporanReturController::class, ['except' => ['show']]);
            Route::resourceWithData('payment-adv', LaporanPaymentADVController::class, ['except' => ['show']]);

            Route::resourceWithData('performa-iklan', LaporanPerformaIklanController::class, ['except' => ['show']]);
            Route::resourceWithData('cashback', LaporanCashBackController::class, ['except' => ['show']]);
            Route::resourceWithData('pengiriman', LaporanPengirimanController::class, ['except' => ['show']]);

            //export excel
        });

        Route::prefix('monitoring')->name('monitoring.')->group(function () {
            Route::get('pengiriman', [MonitoringPengirimanController::class, 'index'])->name('pengiriman');
            Route::get('lacak/{id}', [MonitoringPengirimanController::class, 'lacak'])->name('lacak');
        });

        Route::prefix('wilayah')->name('wilayah.')->group(function () {
            Route::post('get-provinsi', [WilayahController::class, 'getProvinces'])->name('provinsi');
            Route::post('get-kabupaten/{id}', [WilayahController::class, 'getCities'])->name('kabupaten');
            Route::post('get-kecamatan/{id}', [WilayahController::class, 'getDistricts'])->name('kecamatan');
            Route::post('get-kelurahan/{id}', [WilayahController::class, 'getSubDistricts'])->name('kelurahan');
        });

        Route::prefix('kiriminaja')->name('kiriminaja.')->group(function () {
            Route::prefix('kurir')->name('kurir.')->group(function () {
                Route::post('/', [KurirController::class, 'getCurier'])->name('getCurier');
            });
            // schedules
            Route::prefix('schedules')->name('schedules.')->group(function () {
                Route::get('/', [KurirController::class, 'getSchedules'])->name('getSchedules');
            });

            Route::prefix('order')->name('order.')->group(function () {
                Route::post('/', [\App\Http\Controllers\KiriminAja\OrderController::class, 'createOrder'])->name('store');
            });
        });

        Route::prefix('landing-page')->name('landingPage.')->group(function () {
            Route::get('index', [LandingPageController::class, 'index'])->name('index');
            Route::get('data', [LandingPageController::class, 'data'])->name('data');
            Route::get('get-products', [LandingPageController::class, 'getProducts'])->name('getProducts');
            Route::get('builder', [LandingPageController::class, 'create'])->name('builder');
            Route::post('store', [LandingPageController::class, 'store'])->name('store');
            Route::get('{id}/edit', [LandingPageController::class, 'edit'])->name('edit');
            Route::put('{id}', [LandingPageController::class, 'update'])->name('update');
            Route::delete('{id}', [LandingPageController::class, 'destroy'])->name('destroy');
            Route::get('preview/{slug}', [LandingPageController::class, 'preview'])->name('preview');
        });

        Route::prefix('facebook-pixel')->name('facebookPixel.')->group(function () {
            Route::get('get-pixels', [FacebookPixelController::class, 'getPixels'])->name('getPixels');
            Route::post('store', [FacebookPixelController::class, 'store'])->name('store');
            // Route events dan parameters dihapus - implementasi manual sederhana
        });
    });
});

Route::get('/aja/test', [App\Http\Controllers\DataController::class, 'index']);
Route::get('/aja/name', [App\Http\Controllers\DataController::class, 'test']);
// Route::get('/test/test', [KurirController::class, 'getCurier'])->name('test');

// API Key management routes
Route::get('/api-keys', [ApiKeyController::class, 'index'])->name('api-keys.index');
Route::put('/api-keys/update', [ApiKeyController::class, 'update'])->name('api-keys.update');
Route::post('/api-keys/test-connection', [ApiKeyController::class, 'testConnection'])->name('api-keys.test-connection');
Route::post('/api-keys/refresh-config', [ApiKeyController::class, 'refreshConfig'])->name('api-keys.refresh-config');
Route::get('/api-keys/debug', [ApiKeyController::class, 'debugConfig'])->name('api-keys.debug');
Route::get('/api-keys/test-basic', [ApiKeyController::class, 'testBasicConnectivity'])->name('api-keys.test-basic');
