<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\Facebook\OptimizedFacebookAdService;
use App\Models\ApiKey;

echo "🔍 Testing Facebook API Connection...\n\n";

// 1. Check Database Configuration
echo "1. Checking Database Configuration:\n";
$apiKey = ApiKey::first();

if (!$apiKey) {
    echo "❌ No API key configuration found in database\n";
    exit(1);
}

$configStatus = [
    'App ID' => !empty($apiKey->facebook_app_id) ? '✅ SET' : '❌ MISSING',
    'App Secret' => !empty($apiKey->facebook_app_secret) ? '✅ SET' : '❌ MISSING',
    'Access Token' => !empty($apiKey->facebook_access_token) ? '✅ SET' : '❌ MISSING',
];

foreach ($configStatus as $key => $status) {
    echo "   {$key}: {$status}\n";
}
echo "\n";

// 2. Test Service
echo "2. Testing Service:\n";
try {
    $facebookService = app(OptimizedFacebookAdService::class);

    $isConfigured = $facebookService->isConfigured();
    echo "   Service Configured: " . ($isConfigured ? '✅ YES' : '❌ NO') . "\n";

    if ($isConfigured) {
        echo "   Testing connection...\n";
        $connectionTest = $facebookService->testConnection();

        if ($connectionTest['success']) {
            echo "   ✅ Connection successful!\n";
            if (isset($connectionTest['data'])) {
                echo "   User: " . ($connectionTest['data']['user_name'] ?? 'Unknown') . "\n";
            }
        } else {
            echo "   ❌ Connection failed: " . $connectionTest['message'] . "\n";
        }
    }

} catch (\Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n✅ Test completed!\n";
